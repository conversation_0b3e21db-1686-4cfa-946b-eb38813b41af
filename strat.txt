🎯 STRATÉGIE DES 4 MÉTRIQUES CLÉS
=====================================

📊 MÉTRIQUES UTILISÉES (4 uniquement) :
1. CrossT + CrossT_Diff (30%)
2. DivKLT + DivKLT_Diff (30%) 
3. TauxT + TauxT_Diff (20%)
4. BlockT + BlockT_Diff (20%)

🔄 PROCESSUS DE DÉCISION EN 4 ÉTAPES :

ÉTAPE 1 : CLASSEMENT CrossT_Diff (PRIORITÉ ABSOLUE)
- Classer les 6 candidats par CrossT_Diff croissant
- CrossT_Diff faible = EXCELLENT (stabilité prédictive maximale)
- CrossT_Diff élevé = MAUVAIS (rupture du modèle)

ÉTAPE 2 : CLASSEMENT DivKLT_Diff (PRIORITÉ ÉLEVÉE)
- Classer les 6 candidats par DivKLT_Diff croissant
- DivKLT_Diff faible = EXCELLENT (cohérence obs/théorie maintenue)
- DivKLT_Diff élevé = MAUVAIS (divergence croissante)

ÉTAPE 3 : SÉLECTION DES FINALISTES
- Retenir les candidats qui ont AU MOINS :
  - CrossT_Diff dans le TOP 3 OU
  - DivKLT_Diff dans le TOP 3
- Éliminer ceux qui sont mauvais sur les deux

ÉTAPE 4 : DÉPARTAGE FINAL
- SI un candidat domine sur CrossT_Diff → LE CHOISIR
- SINON SI égalité sur CrossT_Diff → regarder DivKLT_Diff
- SINON → choisir le meilleur équilibre global CrossT_Diff + DivKLT_Diff

⚖️ RÈGLES DE DÉPARTAGE PRÉCISES :

SI CrossT_Diff(A) < 0.005 ET CrossT_Diff(B) > 0.010 
ALORS choisir A (différence significative)

SI |CrossT_Diff(A) - CrossT_Diff(B)| < 0.003 
ALORS regarder DivKLT_Diff pour départager

SI les deux sont très proches sur CrossT_Diff ET DivKLT_Diff
ALORS choisir celui avec le meilleur équilibre global

🎯 CRITÈRES SECONDAIRES (TauxT/BlockT) :
- Utilisés UNIQUEMENT en cas d'égalité parfaite sur CrossT_Diff + DivKLT_Diff
- TauxT/BlockT atypiques peuvent être un signal (positif ou négatif)

🚫 CE QUE JE N'UTILISE PAS :
- CondT, MetricT, TopoT, ShannonT (éliminés)
- Valeurs absolues comme critère principal (seulement influence inconsciente)
- Patterns complexes ou formules mathématiques

📈 EXEMPLE D'APPLICATION :

Candidats Main X :
A : CrossT_Diff=0.0020, DivKLT_Diff=0.0150
B : CrossT_Diff=0.0080, DivKLT_Diff=0.0030  
C : CrossT_Diff=0.0050, DivKLT_Diff=0.0200

Décision :
1. CrossT_Diff : A (0.0020) domine → A gagne
2. Pas besoin de regarder plus loin

Autre exemple :
A : CrossT_Diff=0.0020, DivKLT_Diff=0.0150
B : CrossT_Diff=0.0025, DivKLT_Diff=0.0030

Décision :
1. CrossT_Diff : Très proche (0.0020 vs 0.0025)
2. DivKLT_Diff : B domine (0.0030 vs 0.0150) → B gagne

🎯 RÉSUMÉ EN UNE PHRASE :
"Choisir le candidat avec les plus faibles variations (DIFF) sur CrossT et DivKLT, en privilégiant CrossT_Diff comme critère principal."

Cette stratégie privilégie la STABILITÉ et la CONTINUITÉ plutôt que la performance brute.
